#!/bin/bash

# Ensuring jq is installed in our github actions' server
jq --version || sudo apt-get update -y && sudo apt-get install -y jq

# Referencing our temporary sts role from our server
AWS_ACCESS_KEY_ID=$(jq -r '.Credentials.AccessKeyId' /tmp/sts/sts_credentials.json)
AWS_SECRET_ACCESS_KEY=$(jq -r '.Credentials.SecretAccessKey' /tmp/sts/sts_credentials.json)
AWS_SESSION_TOKEN=$(jq -r '.Credentials.SessionToken' /tmp/sts/sts_credentials.json)
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_SESSION_TOKEN

# Getting ID of the application from Amplify
APP_ID=$(
  aws amplify list-apps \
    --query "apps[?name=='$AWS_AMPLIFY_PRODUCTION_APP_NAME'].appId" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --output text
)

# Getting number of applications in AWS Amplify ( as max is 5 )
APPS_TAKEN=$(
  aws amplify list-apps \
    --query "length(apps[*])" \
    --region "$AWS_FRONTEND_SLAVE_REGION"
)

# Getting number of applications' branches in AWS amplify ( as max is 25 )
BRANCHES_TAKEN=$(
  aws amplify list-branches \
    --app-id "$APP_ID" \
    --query "length(branches[*])" \
    --region "$AWS_FRONTEND_SLAVE_REGION"
)

echo "Checking for application availability..."

# CONDITIONS:
# - Application exists in the first place
# - Number of Applications is not 25
# - Number of Application's branches doesn't exceed 50
if [ -z "$APP_ID" ]; then
  echo "Application specified does not exist !"
  exit 1
elif [ "$BRANCHES_TAKEN" -ge 50 ] && [ "$APPS_TAKEN" -eq 25 ]; then
  echo "Application branches full and number of apps reached it's maximum !"
  exit 1
elif [ "$BRANCHES_TAKEN" -ge 50 ] && [ "$APPS_TAKEN" -lt 25 ]; then
  echo "Application branches full, creating a new Application!"
  NAME_AVAILABLE="false"
  NEW_NAME="$AWS_AMPLIFY_PRODUCTION_APP_NAME"
  COUNTER=1
  while [ "$NAME_AVAILABLE" = "false" ]; do
    NEW_NAME="${AWS_AMPLIFY_PRODUCTION_APP_NAME}-${COUNTER}"
    APP_NAME_AVAILABLE=$(
      aws amplify list-apps \
        --query "apps[?name=='$NEW_NAME']" \
        --region "$AWS_FRONTEND_SLAVE_REGION" \
        --output text
    )
    if [ -z "$APP_NAME_AVAILABLE" ]; then
      aws amplify create-app \
        --name "$NEW_NAME" \
        --region "$AWS_FRONTEND_SLAVE_REGION"
      AWS_AMPLIFY_PRODUCTION_APP_NAME="$NEW_NAME"
      APP_ID=$(
        aws amplify list-apps \
          --query "apps[?name=='$NEW_NAME'].appId" \
          --region "$AWS_FRONTEND_SLAVE_REGION" \
          --output text
      )
      NAME_AVAILABLE="true"
    fi
    COUNTER=$((COUNTER + 1))
  done
fi

# Conditions met, application deployment starts...
echo "App available! Starting deployment of $AWS_AMPLIFY_PRODUCTION_APP_NAME"

# Getting Branch of the application from Amplify
BRANCH_EXISTS=$(
  aws amplify get-branch \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --output text 2>/dev/null
)

# CONDITIONS:
# - Branch doesn't exists, creation of branch required
# - Branch exists, no update needed (as the start deployment will work automatically)
if [ -z "$BRANCH_EXISTS" ]; then
  echo "Branch Not found! Creating..."
  aws amplify create-branch \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
    --region "$AWS_FRONTEND_SLAVE_REGION"
else
  echo "Branch found! Updating..."
fi

# Variable for creating name of the temporary s3 subfolder
PROJECT_NAME=$(echo "$GITHUB_REPOSITORY" | cut -d '/' -f 2)

# Deployment of new project on the branch, or updating branch with new deployment
JOB_ID=$(
  aws amplify start-deployment \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
    --source-url "s3://$AWS_S3_BUILD_BUCKET/Blue-Deployments/$PROJECT_NAME/build.zip" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --query "jobSummary.jobId" \
    --output text
)
if [ -z "$AWS_AMPLIFY_PRODUCTION_DOMAIN" ] || [ -z "$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN" ]; then
  echo "custom production domain and/or subdomain not specified, creating a production app without one..."
  while true; do
    STATUS=$(
      aws amplify get-job \
        --app-id "$APP_ID" \
        --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
        --job-id "$JOB_ID" \
        --region "$AWS_FRONTEND_SLAVE_REGION" \
        --query "job.summary.status" \
        --output text
    )
    if [ "$STATUS" == "SUCCEED" ]; then
      SUBDOMAIN_LINK=$(
        echo "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" |
          tr '[:upper:]' '[:lower:]' |
          sed 's/[^a-z0-9-]/-/g'
      )
      echo "Deployment successful, staging website's deployment link is the following:"
      echo "https://${SUBDOMAIN_LINK}.${APP_ID}.amplifyapp.com"
      break
    elif [ "$STATUS" == "FAILED" ]; then
      echo "Deployment failed."
      exit 1
    else
      echo "Deployment is still in progress..."
      sleep 5
    fi
  done
else

  # Checking if application has a domain name associated to it
  DOMAIN_ASSOCIATION_EXISTS=$(
    aws amplify get-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --query "domainAssociation.domainStatus" \
      --region "$AWS_FRONTEND_SLAVE_REGION" \
      --output text 2>/dev/null
  )

  # Checking if application's branch has a sub domain name associated to it
  SUBDOMAIN_ASSOCIATION_EXISTS=$(
    aws amplify get-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --query "domainAssociation.subDomains[?subDomainSetting.prefix=='$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN']" \
      --region "$AWS_FRONTEND_SLAVE_REGION" \
      --output text 2>/dev/null
  )

  # CONDITIONS:
  # - Domain name not associated to the application, we check the availability of the domain on Route53
  #   - If domain available in Route53, associate it to Amplify Application
  #   - Else, project fails, as the domain is not ours or not yet in Route 53
  # - Sub domain name not associated to the application's branch, we associate it to it
  # - Or we don't do anything !
  if [ -z "$DOMAIN_ASSOCIATION_EXISTS" ]; then
    echo "Domain specified not associated with application ! checking if domain is available..."

    # Fetching domain from route 53 if possible
    DOMAIN_AVAILABLE=$(
      aws route53 list-hosted-zones \
        --query "HostedZones[?Name=='$AWS_AMPLIFY_PRODUCTION_DOMAIN.']" \
        --region "$AWS_FRONTEND_SLAVE_REGION" \
        --output text
    )
    if [ -z "$DOMAIN_AVAILABLE" ]; then
      echo "Sorry, domain name is not available in Route 53 ! aborting..."
      exit 1
    else
      echo "domain name is available! associating it to the application..."

      # Creating a temporary subdomain association file
      # What are we doing here ?
      #  - We are creating a new array of object which contains the following:
      #    -> prefix key, which will be the subdomain association for the new project
      #    -> branchName key, which will be the branch name associated with the subdomain
      #  - We are using JQ to add the new prefix and branch name the user inputed in their yaml job variables
      jq -n --arg new_prefix "$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN" --arg new_branchName "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
        '[{ "prefix": $new_prefix | ascii_downcase, "branchName": $new_branchName }]' \
        >newDomainAssociations.json

      aws amplify create-domain-association \
        --app-id "$APP_ID" \
        --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
        --sub-domain-settings file://newDomainAssociations.json \
        --region "$AWS_FRONTEND_SLAVE_REGION"

      # Deleting the domain association after use
      rm newDomainAssociations.json

    fi
  elif [ -z "$SUBDOMAIN_ASSOCIATION_EXISTS" ]; then
    echo "Domain Association Not found! Creating..."

    # Creating a temporary json file that stores all previous sub domain associations
    touch appDomainAssociations.json

    # Retrieving list of previous amplify subdomains
    aws amplify get-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --query "domainAssociation.subDomains[*].subDomainSetting" \
      --region "$AWS_FRONTEND_SLAVE_REGION" |
      cat >>appDomainAssociations.json

    # Adding the new subdomain definition to the list of subdomain associations
    # What are we doing here ?
    #  - We retrieved the JSON file containing the following:
    #    -> prefix keys, which are the subdomains already associated with the app
    #    -> branchName keys, which are the project branch names associated with the subdomains
    #  - We are using JQ to add the new prefix and branch name the user inputed in their yaml job variables
    jq --arg new_prefix "$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN" --arg new_branchName "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
      '. += [{ "prefix": $new_prefix, "branchName": $new_branchName }]' \
      appDomainAssociations.json >tempAssociation.json && mv tempAssociation.json appDomainAssociations.json

    # Updating list of subdomains with the new subdomain added to the list
    aws amplify update-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --sub-domain-settings file://appDomainAssociations.json

    # Deleting the domain association after use
    rm appDomainAssociations.json

  else
    echo "Domain Association found! No actions necessary!"
  fi

  while true; do
    STATUS=$(
      aws amplify get-job \
        --app-id "$APP_ID" \
        --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
        --job-id "$JOB_ID" \
        --region "$AWS_FRONTEND_SLAVE_REGION" \
        --query "job.summary.status" \
        --output text
    )

    if [ "$STATUS" == "SUCCEED" ]; then

      SUBDOMAIN_LINK=$(
        echo "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" |
          tr '[:upper:]' '[:lower:]' |
          sed 's/[^a-z0-9-]/-/g'
      )

      echo "Deployment successful, production website's deployment link is the following:"
      echo "https://${AWS_AMPLIFY_PRODUCTION_SUBDOMAIN}.${AWS_AMPLIFY_PRODUCTION_DOMAIN}"

      break
    elif [ "$STATUS" == "FAILED" ]; then
      echo "Deployment failed."
      exit 1
    else
      echo "Deployment is still in progress..."
      sleep 5
    fi
  done
fi
