name: Build Docker Image

on:
  workflow_call:
    inputs:
      NODE_VERSION:
        type: string
        required: true
      DOCKER_IMAGE:
        type: string
        required: true
      DOCKER_TAG:
        type: string
        required: true
      CONTAINER_PORT:
        type: string
        required: true
      AWS_TARGET_GROUP_PATH:
        type: string
        required: true
      AWS_BACKEND_SSM_ROLE_PATH:
        type: string
        required: true

    secrets:
      AWS_BACKEND_SLAVE_ACCESS_KEY_ID:
        required: true
      AWS_BACKEND_SLAVE_SECRET_ACCESS_KEY:
        required: true
      AWS_BACKEND_SLAVE_REGION:
        required: true
      GH_TOKEN:
        required: true

jobs:
  Docker_Image_Build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: "main"

      - name: Fetching shell scripts from reusable workflow
        run: |
          set -e
          mkdir ./scripts
          curl -s https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/sts/backend_sts_role_generator.sh \
          > ./scripts/backend_sts_role_generator.sh
          curl -s https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/docker/docker_deployment.sh \
          > ./scripts/docker_deployment.sh

      - name: Making the scripts executable
        run: |
          set -e
          chmod +x ./scripts/backend_sts_role_generator.sh
          chmod +x ./scripts/docker_deployment.sh

      - name: Downloading Node.js version ${{ inputs.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.NODE_VERSION }}
          cache: "npm"

      - name: Login to AWS
        id: login-aws
        # Here, I am using an imported actions from github so I can access aws
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_BACKEND_SLAVE_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_BACKEND_SLAVE_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_BACKEND_SLAVE_REGION }}

      - name: STS Role assumption job
        # IAM user by default does not have any permissions, we associate a role to the IAM
        # for better security
        run: |
          set -e
          ./scripts/backend_sts_role_generator.sh
        shell: bash
        env:
          AWS_BACKEND_SSM_ROLE_PATH: ${{ inputs.AWS_BACKEND_SSM_ROLE_PATH }}
          AWS_BACKEND_SLAVE_REGION: ${{ secrets.AWS_BACKEND_SLAVE_REGION }}

      - name: Login, Build and Push of Docker Image
        run: |
          set -e
          ./scripts/docker_deployment.sh
        shell: bash
        env:
          DOCKER_IMAGE: ${{ inputs.DOCKER_IMAGE }}
          DOCKER_TAG: ${{ inputs.DOCKER_TAG }}
          CONTAINER_PORT: ${{ inputs.CONTAINER_PORT }}
          AWS_TARGET_GROUP_PATH: ${{ inputs.AWS_TARGET_GROUP_PATH }}
