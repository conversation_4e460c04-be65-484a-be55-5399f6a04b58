#!/bin/bash

# Ensuring jq is installed in our github actions' server
jq --version || sudo apt-get update -y && sudo apt-get install -y jq

# Referencing our temporary sts role from our server
AWS_ACCESS_KEY_ID=$(jq -r '.Credentials.AccessKeyId' /tmp/sts/sts_credentials.json)
AWS_SECRET_ACCESS_KEY=$(jq -r '.Credentials.SecretAccessKey' /tmp/sts/sts_credentials.json)
AWS_SESSION_TOKEN=$(jq -r '.Credentials.SessionToken' /tmp/sts/sts_credentials.json)
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_SESSION_TOKEN

# Getting ID of the application from Amplify
APP_ID=$(
  aws amplify list-apps \
    --query "apps[?name=='$AWS_AMPLIFY_PRODUCTION_APP_NAME'].appId" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --output text
)

# Getting number of applications in AWS Amplify ( as max is 25 )
APPS_TAKEN=$(
  aws amplify list-apps \
    --query "length(apps[*])" \
    --region "$AWS_FRONTEND_SLAVE_REGION"
)

echo "Checking for application availability..."

# CONDITIONS: Application exists or needs to be created
if [ -z "$APP_ID" ]; then
  if [ "$APPS_TAKEN" -eq 25 ]; then
    echo "Maximum number of applications reached!"
    exit 1
  fi
  
  echo "Application doesn't exist, creating new Next.js application..."
  
  # Create Next.js app with proper platform configuration
  APP_CREATION_RESULT=$(aws amplify create-app \
    --name "$AWS_AMPLIFY_PRODUCTION_APP_NAME" \
    --repository "$GITHUB_REPOSITORY_URL" \
    --platform "WEB_COMPUTE" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --oauth-token "$GITHUB_ACCESS_TOKEN" \
    --build-spec '{
      "version": 1,
      "applications": [
        {
          "appRoot": "/",
          "frontend": {
            "phases": {
              "preBuild": {
                "commands": [
                  "npm ci"
                ]
              },
              "build": {
                "commands": [
                  "npm run build"
                ]
              }
            },
            "artifacts": {
              "baseDirectory": ".next",
              "files": [
                "**/*"
              ]
            },
            "cache": {
              "paths": [
                "node_modules/**/*",
                ".next/cache/**/*"
              ]
            }
          }
        }
      ]
    }' \
    --output json)
  
  APP_ID=$(echo "$APP_CREATION_RESULT" | jq -r '.app.appId')
  
  if [ -z "$APP_ID" ] || [ "$APP_ID" = "null" ]; then
    echo "Failed to create application"
    exit 1
  fi
  
  echo "Created application with ID: $APP_ID"
else
  echo "Application found with ID: $APP_ID"
  
  # Update existing app to ensure it has the right settings for Next.js
  aws amplify update-app \
    --app-id "$APP_ID" \
    --platform "WEB_COMPUTE" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --build-spec '{
      "version": 1,
      "applications": [
        {
          "appRoot": "/",
          "frontend": {
            "phases": {
              "preBuild": {
                "commands": [
                  "npm ci"
                ]
              },
              "build": {
                "commands": [
                  "npm run build"
                ]
              }
            },
            "artifacts": {
              "baseDirectory": ".next",
              "files": [
                "**/*"
              ]
            },
            "cache": {
              "paths": [
                "node_modules/**/*",
                ".next/cache/**/*"
              ]
            }
          }
        }
      ]
    }'
fi

echo "App available! Starting deployment of $AWS_AMPLIFY_PRODUCTION_APP_NAME"

# Getting Branch of the application from Amplify
BRANCH_EXISTS=$(
  aws amplify get-branch \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --output text 2>/dev/null
)

# Create or update branch
if [ -z "$BRANCH_EXISTS" ]; then
  echo "Branch not found! Creating..."
  aws amplify create-branch \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --stage "PRODUCTION" \
    --framework "Next.js - SSR"
else
  echo "Branch found! Will trigger new build..."
fi

# Set environment variables for Next.js if provided
if [ -n "$NEXTJS_ENV_VARS" ] && [ "$NEXTJS_ENV_VARS" != "null" ]; then
  echo "Setting Next.js environment variables..."
  aws amplify update-app \
    --app-id "$APP_ID" \
    --environment-variables "$NEXTJS_ENV_VARS" \
    --region "$AWS_FRONTEND_SLAVE_REGION"
fi

# Start deployment
echo "Starting build and deployment..."
JOB_ID=$(
  aws amplify start-job \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
    --job-type "RELEASE" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --query "jobSummary.jobId" \
    --output text
)

if [ -z "$JOB_ID" ]; then
  echo "Failed to start deployment"
  exit 1
fi

echo "Build started with Job ID: $JOB_ID"

# Handle custom domain if specified
if [ -n "$AWS_AMPLIFY_PRODUCTION_DOMAIN" ] && [ -n "$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN" ]; then
  # Check if domain association exists
  DOMAIN_ASSOCIATION_EXISTS=$(
    aws amplify get-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --query "domainAssociation.domainStatus" \
      --region "$AWS_FRONTEND_SLAVE_REGION" \
      --output text 2>/dev/null
  )

  # Check if subdomain association exists
  SUBDOMAIN_ASSOCIATION_EXISTS=$(
    aws amplify get-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --query "domainAssociation.subDomains[?subDomainSetting.prefix=='$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN']" \
      --region "$AWS_FRONTEND_SLAVE_REGION" \
      --output text 2>/dev/null
  )

  if [ -z "$DOMAIN_ASSOCIATION_EXISTS" ]; then
    echo "Setting up custom domain..."
    
    # Check if domain is available in Route53
    DOMAIN_AVAILABLE=$(
      aws route53 list-hosted-zones \
        --query "HostedZones[?Name=='$AWS_AMPLIFY_PRODUCTION_DOMAIN.']" \
        --region "$AWS_FRONTEND_SLAVE_REGION" \
        --output text
    )
    
    if [ -z "$DOMAIN_AVAILABLE" ]; then
      echo "Domain not available in Route53!"
      exit 1
    fi

    # Create domain association
    jq -n --arg prefix "$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN" --arg branchName "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
      '[{ "prefix": $prefix, "branchName": $branchName }]' \
      > domainAssociations.json

    aws amplify create-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --sub-domain-settings file://domainAssociations.json \
      --region "$AWS_FRONTEND_SLAVE_REGION"

    rm domainAssociations.json
    
  elif [ -z "$SUBDOMAIN_ASSOCIATION_EXISTS" ]; then
    echo "Domain exists but subdomain association not found! Creating..."

    # Get existing subdomains
    aws amplify get-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --query "domainAssociation.subDomains[*].subDomainSetting" \
      --region "$AWS_FRONTEND_SLAVE_REGION" > appDomainAssociations.json

    # Add new subdomain
    jq --arg new_prefix "$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN" --arg new_branchName "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
      '. += [{ "prefix": $new_prefix, "branchName": $new_branchName }]' \
      appDomainAssociations.json > tempAssociation.json && mv tempAssociation.json appDomainAssociations.json

    # Update domain association
    aws amplify update-domain-association \
      --app-id "$APP_ID" \
      --domain-name "$AWS_AMPLIFY_PRODUCTION_DOMAIN" \
      --sub-domain-settings file://appDomainAssociations.json \
      --region "$AWS_FRONTEND_SLAVE_REGION"

    rm appDomainAssociations.json
  else
    echo "Domain and subdomain association found! No action needed."
  fi
fi

# Monitor deployment
echo "Monitoring deployment progress..."
while true; do
  STATUS=$(
    aws amplify get-job \
      --app-id "$APP_ID" \
      --branch-name "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" \
      --job-id "$JOB_ID" \
      --region "$AWS_FRONTEND_SLAVE_REGION" \
      --query "job.summary.status" \
      --output text
  )
  
  if [ "$STATUS" == "SUCCEED" ]; then
    if [ -n "$AWS_AMPLIFY_PRODUCTION_DOMAIN" ] && [ -n "$AWS_AMPLIFY_PRODUCTION_SUBDOMAIN" ]; then
      echo "Deployment successful! Production website is available at:"
      echo "https://${AWS_AMPLIFY_PRODUCTION_SUBDOMAIN}.${AWS_AMPLIFY_PRODUCTION_DOMAIN}"
    else
      SUBDOMAIN_LINK=$(
        echo "$AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME" |
          tr '[:upper:]' '[:lower:]' |
          sed 's/[^a-z0-9-]/-/g'
      )
      echo "Deployment successful! Production website is available at:"
      echo "https://${SUBDOMAIN_LINK}.${APP_ID}.amplifyapp.com"
    fi
    break
  elif [ "$STATUS" == "FAILED" ]; then
    echo "Deployment failed. Check Amplify console for details."
    exit 1
  else
    echo "Deployment in progress... Status: $STATUS"
    sleep 30
  fi
done