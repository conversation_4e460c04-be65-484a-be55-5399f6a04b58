name: Next.js Staging build and hosting pipeline

on:
  pull_request:
    types: [closed]
    branches: ["staging-branch"]

jobs:
  nextjs-staging-deployment:
    if: github.event.pull_request.merged == true
    uses: Modular3D/UniversalCICDPipelines/.github/workflows/amplify_nextjs_staging_job.yml@main
    with:
      AWS_FRONTEND_SSM_ROLE_PATH: ${{ vars.AWS_FRONTEND_SSM_ROLE_PATH }}
      AWS_AMPLIFY_STAGING_APP_NAME: ${{ vars.AWS_AMPLIFY_STAGING_APP_NAME }}
      AWS_AMPLIFY_STAGING_RESOURCE_NAME: ${{ vars.AWS_AMPLIFY_STAGING_RESOURCE_NAME }}
      GITHUB_REPOSITORY_URL: ${{ github.server_url }}/${{ github.repository }}
      NEXTJS_ENV_VARS: ${{ vars.NEXTJS_ENV_VARS }}
    secrets: inherit
