#!/bin/bash

# Installing Project Dependencies
npm install --legacy-peer-deps

# Ensuring jq is installed in our github actions' server
jq --version || sudo apt-get update -y && sudo apt-get install -y jq

# Referencing our temporary sts role from our server
AWS_ACCESS_KEY_ID=$(jq -r '.Credentials.AccessKeyId' /tmp/sts/sts_credentials.json)
AWS_SECRET_ACCESS_KEY=$(jq -r '.Credentials.SecretAccessKey' /tmp/sts/sts_credentials.json)
AWS_SESSION_TOKEN=$(jq -r '.Credentials.SessionToken' /tmp/sts/sts_credentials.json)
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_SESSION_TOKEN

# Making project's sourcemap invisible, + optionally adding a prefix as this project holds two solutions
GENERATE_SOURCEMAP=false && npm run build

# Variable for creating name for a temporary s3 subfolder
PROJECT_NAME=$(echo "$GITHUB_REPOSITORY" | cut -d '/' -f 2)

cd build || return

# Zipping project to send on S3
zip -r ../build.zip .

cd ..

# Sending the build project file to our s3 bucket
aws s3 cp \
  build.zip s3://"$AWS_S3_BUILD_BUCKET"/Green-Deployments/"$PROJECT_NAME"/ \
  --region "$AWS_FRONTEND_SLAVE_REGION"

# Congrats! the build project has been sent !
echo "Frontend project build and s3 deployment is complete !"
