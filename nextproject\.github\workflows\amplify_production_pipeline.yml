name: NEXT.JS PRODUCTION TRIGGER

on:
  workflow_dispatch:
  push:
    branches: [main] # Optional: Auto-deploy on main branch push

jobs:
  nextjs-amplify-deployment:
    uses: Modular3D/UniversalCICDPipelines/.github/workflows/amplify_nextjs_production_job.yml@main
    with:
      AWS_FRONTEND_SSM_ROLE_PATH: ${{ vars.AWS_FRONTEND_SSM_ROLE_PATH }}
      AWS_AMPLIFY_PRODUCTION_APP_NAME: ${{ vars.AWS_AMPLIFY_PRODUCTION_APP_NAME }}
      AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME: ${{ vars.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}
      AWS_AMPLIFY_PRODUCTION_DOMAIN: ${{ vars.AWS_AMPLIFY_PRODUCTION_DOMAIN }}
      AWS_AMPLIFY_PRODUCTION_SUBDOMAIN: ${{ vars.A<PERSON>_AMPLIFY_PRODUCTION_SUBDOMAIN }}
      GITHUB_REPOSITORY_URL: ${{ github.server_url }}/${{ github.repository }}
      NEXTJS_ENV_VARS: ${{ vars.NEXTJS_ENV_VARS }}
    secrets: inherit
