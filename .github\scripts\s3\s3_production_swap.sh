#!/bin/bash

# Ensuring jq is installed in our github actions' server
jq --version || sudo apt-get update -y && sudo apt-get install -y jq

# Referencing our temporary sts role from our server
AWS_ACCESS_KEY_ID=$(jq -r '.Credentials.AccessKeyId' /tmp/sts/sts_credentials.json)
AWS_SECRET_ACCESS_KEY=$(jq -r '.Credentials.SecretAccessKey' /tmp/sts/sts_credentials.json)
AWS_SESSION_TOKEN=$(jq -r '.Credentials.SessionToken' /tmp/sts/sts_credentials.json)
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_SESSION_TOKEN

# Variable for creating name for a temporary s3 subfolder
Project_Name=$(echo "$GITHUB_REPOSITORY" | cut -d '/' -f 2)

# Checking if staging bucket exists
Staging_Exists=$(
  aws s3 ls \
    "s3://$AWS_S3_BUILD_BUCKET/Green-Deployments/$Project_Name" 2>/dev/null || true
)

# Disallowing production swapping if staging does not exist
# Why? We need to stage our frontend projects first and test them
# When all tests are complete, we then can deploy an error prone production project
if [ -z "$Staging_Exists" ]; then
  echo "Cannot deploy a production project without it being staged first!"
  exit 1
fi

# For setting the version number of the production, I am counting the number of deployed versions
NumberOfBuilds=$(
  aws s3api list-object-versions \
    --bucket "$AWS_S3_BUILD_BUCKET" \
    --prefix Blue-Deployments/"$Project_Name"/build.zip \
    --query "length(Versions)" \
    2>/dev/null
)
VersionNumber=""

# built for 1 -> 1000 build versions
if [ -z "$NumberOfBuilds" ]; then
  VersionNumber=000
elif [ "$NumberOfBuilds" -lt 10 ]; then
  VersionNumber=00"$NumberOfBuilds"
elif [ "$NumberOfBuilds" -lt 100 ]; then
  VersionNumber=0"$NumberOfBuilds"
elif [ "$NumberOfBuilds" -lt 1000 ]; then
  VersionNumber="$NumberOfBuilds"
else
  echo "You exceeded 1000 versions for this app, did not take such scenario into account"
  exit 1
fi

# Sending the Staging build to Production Folder in S3
aws s3 cp \
  "s3://$AWS_S3_BUILD_BUCKET/Green-Deployments/$Project_Name/build.zip" "s3://$AWS_S3_BUILD_BUCKET/Blue-Deployments/$Project_Name/build.zip" \
  --region "$AWS_FRONTEND_SLAVE_REGION" \
  --metadata "Version=v1.$VersionNumber"

# Removing the Staged app
aws s3 rm \
  "s3://$AWS_S3_BUILD_BUCKET/Green-Deployments/$Project_Name/" \
  --recursive \
  --region "$AWS_FRONTEND_SLAVE_REGION"

# Congrats! the build project has been sent !
echo "Frontend swapping of staging to production on S3 successful !"
