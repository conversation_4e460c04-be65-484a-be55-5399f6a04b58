name: NPM auto package update job

on:
  workflow_call:
    inputs:
      PU_BR<PERSON>CH_REFERENCE:
        type: string
        required: true
    secrets:
      GH_TOKEN:
        required: true

jobs:
  Jest_Testing:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ inputs.PU_<PERSON><PERSON>CH_REFERENCE }}
          persist-credentials: false
          fetch-depth: 0

      - name: Fetching shell scripts from reusable workflow
        run: |
          set -e
          mkdir ./scripts
          curl -s https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/npm/npm_package_update.sh \
          > ./scripts/npm_package_update.sh

      - name: Making the script executable
        run: |
          set -e
          chmod +x ./scripts/npm_package_update.sh

      - name: Updating NPM packages to the latest version
        run: |
          set -e
          source ./scripts/npm_package_update.sh
          export AllowedToPush=$?
        shell: bash

      - name: Deploying new package.json version to repository
        if: ${{ env.AllowedToPush  == '0' }}
        uses: actions-js/push@master
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          message: "Automated update from Github Action's CRON job"
          branch: ${{ inputs.PU_BRANCH_REFERENCE }}
