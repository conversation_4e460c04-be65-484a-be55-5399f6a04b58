#!/bin/bash

# Temporary folder to store sts role credentials
mkdir /tmp/sts

# Retrieving role parameter for Slave User
SlaveFrontendRole=$(
  aws ssm get-parameter \
    --name "$AWS_FRONTEND_SSM_ROLE_PATH" \
    --query "Parameter.Value" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --output text
)

# Attaching role of Slave User in a temporary json file
# Available for only 15 mins, security best practice
aws sts assume-role \
  --role-arn "$SlaveFrontendRole" \
  --role-session-name "SlaveFrontendSession" \
  --duration-seconds 900 |
  cat >/tmp/sts/sts_credentials.json

echo "Role assumption is complete !"
