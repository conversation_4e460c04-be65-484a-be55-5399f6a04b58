name: Next.js Production job for Amplify deployment

on:
  workflow_call:
    inputs:
      AWS_AMPLIFY_PRODUCTION_APP_NAME:
        required: true
        type: string
      AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME:
        required: true
        type: string
      AWS_FRONTEND_SSM_ROLE_PATH:
        required: true
        type: string
      AWS_AMPLIFY_PRODUCTION_DOMAIN:
        required: false
        type: string
      AWS_AMPLIFY_PRODUCTION_SUBDOMAIN:
        required: false
        type: string
      GITHUB_REPOSITORY_URL:
        required: true
        type: string
      NEXTJS_ENV_VARS:
        required: false
        type: string
    secrets:
      AWS_FRONTEND_SLAVE_ACCESS_KEY_ID:
        required: true
      AWS_FRONTEND_SLAVE_SECRET_ACCESS_KEY:
        required: true
      AWS_FRONTEND_SLAVE_REGION:
        required: true
      GH_TOKEN:
        required: true

jobs:
  AWS_NextJS_Production_Deployment:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Fetching shell scripts from reusable workflow
        run: |
          set -e
          mkdir ./scripts

          echo "Fetching STS script..."
          HTTP_CODE=$(curl -w "%{http_code}" -s -o ./scripts/frontend_sts_role_generator.sh \
            "https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/sts/frontend_sts_role_generator.sh")
          echo "STS script HTTP response code: $HTTP_CODE"

          if [ "$HTTP_CODE" != "200" ]; then
            echo "Failed to fetch STS script. Response content:"
            cat ./scripts/frontend_sts_role_generator.sh
            exit 1
          fi

          echo "Fetching Amplify script..."
          HTTP_CODE=$(curl -w "%{http_code}" -s -o ./scripts/amplify_nextjs_production_deployment.sh \
            "https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/amplify/amplify_nextjs_production_deployment.sh")
          echo "Amplify script HTTP response code: $HTTP_CODE"

          if [ "$HTTP_CODE" != "200" ]; then
            echo "Failed to fetch Amplify script. Response content:"
            cat ./scripts/amplify_nextjs_production_deployment.sh
            exit 1
          fi

          echo "Verifying script content..."
          echo "STS script first line:"
          head -1 ./scripts/frontend_sts_role_generator.sh
          echo "Amplify script first line:"
          head -1 ./scripts/amplify_nextjs_production_deployment.sh

          echo "Scripts fetched successfully"

      - name: Making the scripts executable
        run: |
          set -e
          chmod +x ./scripts/frontend_sts_role_generator.sh
          chmod +x ./scripts/amplify_nextjs_production_deployment.sh

      - name: Login to AWS
        id: login-aws
        # Here, I am using an imported actions from github so I can access aws
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_FRONTEND_SLAVE_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_FRONTEND_SLAVE_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}

      - name: STS Role assumption job
        # IAM user by default does not have any permissions, we associate a role to the IAM
        # for better security
        run: |
          set -e
          ./scripts/frontend_sts_role_generator.sh
        shell: bash
        env:
          AWS_FRONTEND_SSM_ROLE_PATH: ${{ inputs.AWS_FRONTEND_SSM_ROLE_PATH }}
          AWS_FRONTEND_SLAVE_REGION: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}

      - name: Deployment of Next.js project on AWS Amplify
        id: deploy-aws
        run: |
          set -e
          ./scripts/amplify_nextjs_production_deployment.sh
        shell: bash
        env:
          AWS_FRONTEND_SLAVE_REGION: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}
          AWS_AMPLIFY_PRODUCTION_APP_NAME: ${{ inputs.AWS_AMPLIFY_PRODUCTION_APP_NAME }}
          AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME: ${{ inputs.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}
          AWS_AMPLIFY_PRODUCTION_DOMAIN: ${{ inputs.AWS_AMPLIFY_PRODUCTION_DOMAIN }}
          AWS_AMPLIFY_PRODUCTION_SUBDOMAIN: ${{ inputs.AWS_AMPLIFY_PRODUCTION_SUBDOMAIN }}
          GITHUB_REPOSITORY_URL: ${{ inputs.GITHUB_REPOSITORY_URL }}
          GITHUB_ACCESS_TOKEN: ${{ secrets.GH_TOKEN }}
          NEXTJS_ENV_VARS: ${{ inputs.NEXTJS_ENV_VARS }}
