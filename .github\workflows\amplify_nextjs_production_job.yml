name: Next.js Production Deployment to AWS Amplify

on:
  workflow_call:
    inputs:
      AWS_AMPLIFY_PRODUCTION_APP_NAME:
        required: true
        type: string
      AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME:
        required: true
        type: string
      AWS_FRONTEND_SSM_ROLE_PATH:
        required: true
        type: string
      AWS_AMPLIFY_PRODUCTION_DOMAIN:
        required: false
        type: string
      AWS_AMPLIFY_PRODUCTION_SUBDOMAIN:
        required: false
        type: string
      GITHUB_REPOSITORY_URL:
        required: true
        type: string
      NEXTJS_ENV_VARS:
        required: false
        type: string
    secrets:
      AWS_FRONTEND_SLAVE_ACCESS_KEY_ID:
        required: true
      AWS_FRONTEND_SLAVE_SECRET_ACCESS_KEY:
        required: true
      AWS_FRONTEND_SLAVE_REGION:
        required: true
      GH_TOKEN:
        required: true

jobs:
  nextjs-production-deployment:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Install jq
        run: sudo apt-get update && sudo apt-get install -y jq

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_FRONTEND_SLAVE_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_FRONTEND_SLAVE_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}

      - name: Assume STS Role
        run: |
          # Get role ARN from SSM Parameter Store
          ROLE_ARN=$(aws ssm get-parameter \
            --name "${{ inputs.AWS_FRONTEND_SSM_ROLE_PATH }}" \
            --query "Parameter.Value" \
            --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}" \
            --output text)

          # Assume the role
          CREDENTIALS=$(aws sts assume-role \
            --role-arn "$ROLE_ARN" \
            --role-session-name "NextJSProductionSession" \
            --duration-seconds 3600 \
            --output json)

          # Export the temporary credentials
          echo "AWS_ACCESS_KEY_ID=$(echo $CREDENTIALS | jq -r '.Credentials.AccessKeyId')" >> $GITHUB_ENV
          echo "AWS_SECRET_ACCESS_KEY=$(echo $CREDENTIALS | jq -r '.Credentials.SecretAccessKey')" >> $GITHUB_ENV
          echo "AWS_SESSION_TOKEN=$(echo $CREDENTIALS | jq -r '.Credentials.SessionToken')" >> $GITHUB_ENV

      - name: Deploy to AWS Amplify
        run: |
          # Check if app exists
          APP_ID=$(aws amplify list-apps \
            --query "apps[?name=='${{ inputs.AWS_AMPLIFY_PRODUCTION_APP_NAME }}'].appId" \
            --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}" \
            --output text)

          # Create app if it doesn't exist
          if [ -z "$APP_ID" ]; then
            echo "Creating new Amplify app..."
            APP_RESULT=$(aws amplify create-app \
              --name "${{ inputs.AWS_AMPLIFY_PRODUCTION_APP_NAME }}" \
              --repository "${{ inputs.GITHUB_REPOSITORY_URL }}" \
              --platform "WEB_COMPUTE" \
              --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}" \
              --oauth-token "${{ secrets.GH_TOKEN }}" \
              --build-spec '{
                "version": 1,
                "applications": [
                  {
                    "appRoot": "/",
                    "frontend": {
                      "phases": {
                        "preBuild": {
                          "commands": [
                            "npm ci"
                          ]
                        },
                        "build": {
                          "commands": [
                            "npm run build"
                          ]
                        }
                      },
                      "artifacts": {
                        "baseDirectory": ".next",
                        "files": [
                          "**/*"
                        ]
                      },
                      "cache": {
                        "paths": [
                          "node_modules/**/*",
                          ".next/cache/**/*"
                        ]
                      }
                    }
                  }
                ]
              }' \
              --output json)

            APP_ID=$(echo "$APP_RESULT" | jq -r '.app.appId')
            echo "Created app with ID: $APP_ID"
          else
            echo "Found existing app with ID: $APP_ID"
          fi

          # Check if branch exists
          BRANCH_EXISTS=$(aws amplify get-branch \
            --app-id "$APP_ID" \
            --branch-name "${{ inputs.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}" \
            --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}" \
            --output text 2>/dev/null || echo "")

          # Create branch if it doesn't exist
          if [ -z "$BRANCH_EXISTS" ]; then
            echo "Creating branch..."
            aws amplify create-branch \
              --app-id "$APP_ID" \
              --branch-name "${{ inputs.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}" \
              --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}" \
              --stage "PRODUCTION" \
              --framework "Next.js - SSR"
          fi

          # Set environment variables if provided
          if [ -n "${{ inputs.NEXTJS_ENV_VARS }}" ] && [ "${{ inputs.NEXTJS_ENV_VARS }}" != "null" ]; then
            echo "Setting environment variables..."
            aws amplify update-app \
              --app-id "$APP_ID" \
              --environment-variables '${{ inputs.NEXTJS_ENV_VARS }}' \
              --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}"
          fi

          # Set up custom domain if provided
          if [ -n "${{ inputs.AWS_AMPLIFY_PRODUCTION_DOMAIN }}" ] && [ "${{ inputs.AWS_AMPLIFY_PRODUCTION_DOMAIN }}" != "null" ]; then
            echo "Setting up custom domain..."
            SUBDOMAIN="${{ inputs.AWS_AMPLIFY_PRODUCTION_SUBDOMAIN }}"
            if [ -z "$SUBDOMAIN" ] || [ "$SUBDOMAIN" = "null" ]; then
              SUBDOMAIN=""
            fi

            aws amplify create-domain-association \
              --app-id "$APP_ID" \
              --domain-name "${{ inputs.AWS_AMPLIFY_PRODUCTION_DOMAIN }}" \
              --sub-domain-settings "prefix=$SUBDOMAIN,branchName=${{ inputs.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}" \
              --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}" || echo "Domain may already exist"
          fi

          # Start deployment
          echo "Starting deployment..."
          JOB_ID=$(aws amplify start-job \
            --app-id "$APP_ID" \
            --branch-name "${{ inputs.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}" \
            --job-type "RELEASE" \
            --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}" \
            --query "jobSummary.jobId" \
            --output text)

          echo "Deployment started with Job ID: $JOB_ID"

          # Monitor deployment
          while true; do
            STATUS=$(aws amplify get-job \
              --app-id "$APP_ID" \
              --branch-name "${{ inputs.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}" \
              --job-id "$JOB_ID" \
              --region "${{ secrets.AWS_FRONTEND_SLAVE_REGION }}" \
              --query "job.summary.status" \
              --output text)

            echo "Deployment status: $STATUS"

            if [ "$STATUS" = "SUCCEED" ]; then
              SUBDOMAIN_LINK=$(echo "${{ inputs.AWS_AMPLIFY_PRODUCTION_RESOURCE_NAME }}" | tr '[:upper:]' '[:lower:]' | sed 's/[^a-z0-9-]/-/g')
              echo "🎉 Deployment successful!"
              echo "Production URL: https://${SUBDOMAIN_LINK}.${APP_ID}.amplifyapp.com"

              if [ -n "${{ inputs.AWS_AMPLIFY_PRODUCTION_DOMAIN }}" ] && [ "${{ inputs.AWS_AMPLIFY_PRODUCTION_DOMAIN }}" != "null" ]; then
                CUSTOM_SUBDOMAIN="${{ inputs.AWS_AMPLIFY_PRODUCTION_SUBDOMAIN }}"
                if [ -n "$CUSTOM_SUBDOMAIN" ] && [ "$CUSTOM_SUBDOMAIN" != "null" ]; then
                  echo "Custom URL: https://${CUSTOM_SUBDOMAIN}.${{ inputs.AWS_AMPLIFY_PRODUCTION_DOMAIN }}"
                else
                  echo "Custom URL: https://${{ inputs.AWS_AMPLIFY_PRODUCTION_DOMAIN }}"
                fi
              fi
              break
            elif [ "$STATUS" = "FAILED" ]; then
              echo "❌ Deployment failed!"
              exit 1
            else
              echo "⏳ Deployment in progress..."
              sleep 30
            fi
          done
