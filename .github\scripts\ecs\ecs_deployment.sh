#!/bin/bash

# Ensuring jq is installed in our github actions' server
jq --version || sudo apt-get update -y && sudo apt-get install -y jq

# Referencing our temporary sts role from our server
AWS_ACCESS_KEY_ID=$(jq -r '.Credentials.AccessKeyId' /tmp/sts/sts_credentials.json)
AWS_SECRET_ACCESS_KEY=$(jq -r '.Credentials.SecretAccessKey' /tmp/sts/sts_credentials.json)
AWS_SESSION_TOKEN=$(jq -r '.Credentials.SessionToken' /tmp/sts/sts_credentials.json)
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_SESSION_TOKEN

# Retrieving the Task Definition's ARN if it exists
TASK_ARN=$(
  aws ecs describe-task-definition \
    --task-definition "${AWS_PROJECT_NAME}Task" \
    --query "taskDefinition.taskDefinitionArn" \
    --region "$AWS_BACKEND_SLAVE_REGION" \
    --output text
) || true

# Retrieving the Target Group ARN if it exists
TARGET_GROUP_ARN=$(
  aws elbv2 describe-target-groups \
    --names "${AWS_PROJECT_NAME}-TG" \
    --query "TargetGroups[0].TargetGroupArn" \
    --region "$AWS_BACKEND_SLAVE_REGION" \
    --output text
) || true

# Retrieving the Service's ARN if it exists
SERVICE_ARN=$(
  aws ecs describe-services \
    --cluster "$AWS_CLUSTER_NAME" \
    --service "${AWS_PROJECT_NAME}Service" \
    --query "services[0].serviceArn" \
    --region "$AWS_BACKEND_SLAVE_REGION" \
    --output text
) || true

if [ "$SERVICE_ARN" = "None" ]; then
  SERVICE_ARN=""
fi

# Retrieving the Load Balancers' rule ARN
RULE_ARN=$(
  aws elbv2 describe-rules \
    --rule-arn "$AWS_LOAD_BALANCER_RULE_ARN" \
    --query "Rules[0].RuleArn" \
    --region "$AWS_BACKEND_SLAVE_REGION" \
    --output text
) || true

# Condition of Task Definition's existance
# If Task Definition's ARN is not found, create a new one from our yaml definition file
# see ./.github/definitions/container-definition.yml
if [ -z "$TASK_ARN" ]; then
  echo "Task definition not found. Creating a new one..."
  aws ecs register-task-definition \
    --cli-input-yaml "file://${TASK_DEFINITION_PATH}" \
    --region "$AWS_BACKEND_SLAVE_REGION"
  TASK_ARN=$(
    aws ecs describe-task-definition \
      --task-definition "${AWS_PROJECT_NAME}Task" \
      --query "taskDefinition.taskDefinitionArn" \
      --region "$AWS_BACKEND_SLAVE_REGION" \
      --output text
  )
fi

echo "Project's Task Definition is available"

# Condition of Target Group's existance
# If target Group does not exist, a new one is created
if [ -z "$TARGET_GROUP_ARN" ]; then
  echo "Target group not found. Creating a new one..."
  TARGET_GROUP_ARN=$(
    aws elbv2 create-target-group \
      --name "${AWS_PROJECT_NAME}-TG" \
      --vpc-id "$AWS_VPC_ID" \
      --protocol HTTP \
      --port 80 \
      --health-check-path "$AWS_TARGET_GROUP_PATH/main" \
      --healthy-threshold-count 2 \
      --unhealthy-threshold-count 2 \
      --matcher HttpCode=200-299 \
      --query "TargetGroups[0].TargetGroupArn" \
      --region "$AWS_BACKEND_SLAVE_REGION" \
      --output text
  )
fi

echo "Project's Target Group is available"

# Condition of Load Balancer Rule's existance
# If Load Balancer Rule does not exist, a new one is created
# Otherwise we make sure that the Rule's association with the Target Group is set
# End result will ensure Rule is available, and associated with the Target Group
if [ -z "$RULE_ARN" ]; then
  echo "Rule not found. Creating a new one..."
  # Fetching the last priority number -2 ( as -1 is the 'default' priority number, we want the one before it)
  MAX_PRIORITY=$(
    aws elbv2 describe-rules \
      --listener-arn "$AWS_LOAD_BALANCER_LISTENER_ARN" \
      --query "Rules[-2].Priority" \
      --region "$AWS_BACKEND_SLAVE_REGION" \
      --output text
  )
  # If there are no existing rules, set the priority to 1
  if [ -z "$MAX_PRIORITY" ]; then
    PRIORITY=1
  else
    # Increment the maximum priority by 1
    PRIORITY=$((MAX_PRIORITY + 1))
  fi
  RULE_ARN=$(
    aws elbv2 create-rule \
      --listener-arn "$AWS_LOAD_BALANCER_LISTENER_ARN" \
      --priority "$PRIORITY" \
      --actions Type=forward,TargetGroupArn="$TARGET_GROUP_ARN" \
      --conditions Field=path-pattern,Values="$AWS_TARGET_GROUP_PATH/*" \
      --query "Rules[0].RuleArn" \
      --region "$AWS_BACKEND_SLAVE_REGION" \
      --output text
  )
else
  echo "Rule is found."
  TARGET_GROUP_ARN_EXISTS=$(
    aws elbv2 describe-rules \
      --rule "$RULE_ARN" \
      --query "Rules[0].Actions[0].TargetGroupArn" \
      --region "$AWS_BACKEND_SLAVE_REGION" \
      --output text
  )
  if [ -z "$TARGET_GROUP_ARN_EXISTS" ]; then
    echo "Target group not associated with the rule. Updating the rule..."
    aws elbv2 modify-rule \
      --rule-arn "$RULE_ARN" \
      --actions Type=forward,TargetGroupArn="$TARGET_GROUP_ARN" \
      --region "$AWS_BACKEND_SLAVE_REGION"
  else
    echo "Target Group already associated with the rule."
  fi
fi

echo "Project's Load Balancer Rule is available"

# Condition of Service's existance
# We either create a new one or we remove the last deployed Task
# Cloudformation's Drift Detection will re-deploy the number
# Of tasks set associated with the service
if [ -z "$SERVICE_ARN" ]; then
  echo "Service not found. Creating a new one..."
  aws ecs create-service \
    --cluster "$AWS_CLUSTER_NAME" \
    --service-name "${AWS_PROJECT_NAME}Service" \
    --task-definition "$TASK_ARN" \
    --desired-count "$AWS_TASKS_TO_RUN_NUMBERS" \
    --launch-type "EC2" \
    --load-balancers targetGroupArn="$TARGET_GROUP_ARN",containerName="$DOCKER_IMAGE",containerPort="$CONTAINER_PORT" \
    --region "$AWS_BACKEND_SLAVE_REGION"
  SERVICE_ARN=$(
    aws ecs describe-services \
      --cluster "$AWS_CLUSTER_NAME" \
      --service "${AWS_PROJECT_NAME}Service" \
      --query "services[0].serviceArn" \
      --region "$AWS_BACKEND_SLAVE_REGION" \
      --output text
  )
else
  echo "Updating service with task definition $TASK_ARN..."
  TASK_LIST=$(
    aws ecs list-tasks \
      --cluster "$AWS_CLUSTER_NAME" \
      --service "${AWS_PROJECT_NAME}Service" \
      --query "taskArns" \
      --region "$AWS_BACKEND_SLAVE_REGION" \
      --output text
  )
  for task in "${TASK_LIST[@]}"; do
    aws ecs stop-task \
      --cluster "$AWS_CLUSTER_NAME" \
      --task "$task" \
      --region "$AWS_BACKEND_SLAVE_REGION"
  done
fi

echo "Project's Service is available, project should be now up and running !"
