name: Staging job for Amplify deployment

on:
  workflow_call:
    inputs:
      AWS_S3_BUILD_BUCKET:
        type: string
        required: true
      AWS_AMPLIFY_STAGING_APP_NAME:
        type: string
        required: true
      AWS_AMPLIFY_STAGING_RESOURCE_NAME:
        type: string
        required: true
      AWS_FRONTEND_SSM_ROLE_PATH:
        type: string
        required: true
    secrets:
      AWS_FRONTEND_SLAVE_ACCESS_KEY_ID:
        required: true
      AWS_FRONTEND_SLAVE_SECRET_ACCESS_KEY:
        required: true
      AWS_FRONTEND_SLAVE_REGION:
        required: true
      GH_TOKEN:
        required: true

jobs:
  AWS_User_Login:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: "staging-branch"

      - name: Fetching shell scripts from reusable workflow
        run: |
          set -e
          mkdir ./scripts
          curl -s https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/sts/frontend_sts_role_generator.sh \
          > ./scripts/frontend_sts_role_generator.sh
          curl -s https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/amplify/amplify_staging_deployment.sh \
          > ./scripts/amplify_staging_deployment.sh

      - name: Making the scripts executable
        run: |
          set -e
          chmod +x ./scripts/frontend_sts_role_generator.sh
          chmod +x ./scripts/amplify_staging_deployment.sh

      - name: Login to AWS
        id: login-aws
        # Here, I am using an imported actions from github so I can access aws
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_FRONTEND_SLAVE_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_FRONTEND_SLAVE_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}

      - name: STS Role assumption job
        # IAM user by default does not have any permissions, we associate a role to the IAM
        # for better security
        run: |
          set -e
          ./scripts/frontend_sts_role_generator.sh
        shell: bash
        env:
          AWS_FRONTEND_SSM_ROLE_PATH: ${{ inputs.AWS_FRONTEND_SSM_ROLE_PATH }}
          AWS_FRONTEND_SLAVE_REGION: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}

      - name: Deployment of project on AWS Amplify
        id: deploy-aws
        run: |
          set -e
          ./scripts/amplify_staging_deployment.sh
        shell: bash
        env:
          AWS_FRONTEND_SLAVE_REGION: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}
          AWS_S3_BUILD_BUCKET: ${{ inputs.AWS_S3_BUILD_BUCKET }}
          AWS_AMPLIFY_STAGING_APP_NAME: ${{ inputs.AWS_AMPLIFY_STAGING_APP_NAME }}
          AWS_AMPLIFY_STAGING_RESOURCE_NAME: ${{ inputs.AWS_AMPLIFY_STAGING_RESOURCE_NAME }}
