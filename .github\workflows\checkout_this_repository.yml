name: Staging job for Amplify deployment

on:
  workflow_call:
    secrets:
      GH_TOKEN:
        required: true

jobs:
  Fetching_Scripts:
    runs-on: ubuntu-latest
    steps:
      - name: Fetching current repository
        uses: actions/checkout@v4
        with:
          ref: "main"
          persist-credentials: false

      - name: Fetching private shell scripts
        uses: actions/checkout@v4
        with:
          repository: "Modular3D/UniversalCICDPipelines"
          token: ${{ secrets.GH_TOKEN }}
          ref: "main"
          path: Scripts
          sparse-checkout: |
            .github/scripts/testing/helloWorld.sh
            .github/scripts/sts/backend_sts_role_generator.sh
          sparse-checkout-cone-mode: false

      - name: Printing the response of shell script
        run: |
          set -e
          chmod +x ./Scripts/.github/scripts/testing/helloWorld.sh
          ./Scripts/.github/scripts/testing/helloWorld.sh
        shell: bash
