#!/bin/bash

# Downloading npm check updates
npm install npm-check-updates@latest

# Downloading serve package to test out the project locally
npm install serve@latest

# Updating the packages
ncu -u

# Downloading the new package versions
npm install --legacy-peer-deps

# Building our project to run with serve package
npm run build

# Serving the Project
serve -d ./build -p 8080 2>/dev/null &

ProjectStatusCode=$(curl -o /dev/null -w "%{http_code}" http://localhost:8080/)

if [ "$ProjectStatusCode" -ne 200 ]; then
  echo "Error building the project with new version, abording..."
  exit 1
else
  # Retrieving the job ID of the background serve job
  ProjectBackgroundJobID=$(jobs -p %"serve -d ./build -p 8080 2>/dev/null")
  # Stopping the Background Job
  kill "$ProjectBackgroundJobID"
  echo "Build with new version succesfull ! Deployment of new npm project versions in progress..."
  exit 0
fi
