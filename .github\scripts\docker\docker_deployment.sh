#!/bin/bash

# Ensuring jq is installed in our github actions' server
jq --version || sudo apt-get update -y && sudo apt-get install -y jq

# Referencing our temporary sts role from our server
AWS_ACCESS_KEY_ID=$(jq -r '.Credentials.AccessKeyId' /tmp/sts/sts_credentials.json)
AWS_SECRET_ACCESS_KEY=$(jq -r '.Credentials.SecretAccessKey' /tmp/sts/sts_credentials.json)
AWS_SESSION_TOKEN=$(jq -r '.Credentials.SessionToken' /tmp/sts/sts_credentials.json)
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_SESSION_TOKEN

# Retrieving DockerHub's Username from AWS' SSM Parameter Store
DockerHub_Username=$(
  aws ssm get-parameter \
    --name /general/dockerhub/access-key \
    --query "Parameter.Value" \
    --output text
)

# Retrieving DockerHub's Secret Access Key from AWS' SSM Parameter Store
DockerHub_Secret_Access_Key=$(
  aws ssm get-parameter \
    --name /general/dockerhub/secret-access-key \
    --with-decryption \
    --query "Parameter.Value" \
    --output text
)

# Setting the docker credentials from AWS' SSM Parameter Store values retrieved
echo "$DockerHub_Secret_Access_Key" | docker login -u "$DockerHub_Username" --password-stdin

# Building Project's Docker Image
docker build . -t "$DockerHub_Username"/"$DOCKER_IMAGE"

# Running the Image for testing purposes
docker run -d -p "$CONTAINER_PORT":"$CONTAINER_PORT" "$DockerHub_Username"/"$DOCKER_IMAGE":"$DOCKER_TAG"

sleep 5

curl -I -s http://localhost:"$CONTAINER_PORT""$AWS_TARGET_GROUP_PATH"/main

# Curling the Ran Docker Image's url
Docker_Response=$(curl -I -s http://localhost:"$CONTAINER_PORT""$AWS_TARGET_GROUP_PATH"/main)

# In case there is an error ( could be variable case sensitivity or anything )
# We stop the pipeline
if [[ -z "$Docker_Response" || "$Docker_Response" == *"HTTP/1.1 5"* ]]; then
  echo "Built Docker image has issues, please check code for errors, mispelling, case sensitivity etc..."
  exit 1
fi

# Pushing the Docker Image to DockerHub
docker push "$DockerHub_Username"/"$DOCKER_IMAGE":"$DOCKER_TAG"
