name: Jest Testing of Backend Project

on:
  workflow_call:
    inputs:
      NODE_VERSION:
        type: string
        required: true
    secrets:
      GH_TOKEN:
        required: true

jobs:
  Jest_Testing:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: "main"

      - name: Fetching shell scripts from reusable workflow
        run: |
          set -e
          mkdir ./scripts
          curl -s https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/jest/jest_test.sh \
          > ./scripts/jest_test.sh

      - name: Making the scripts executable
        run: |
          set -e
          chmod +x ./scripts/jest_test.sh

      - name: Downloading Node.js version ${{ inputs.NODE_VERSION }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ inputs.NODE_VERSION }}
          cache: "npm"

      - id: testing-step
        name: Dependencies installation and jest tests
        run: |
          set -e
          ./scripts/jest_test.sh
        shell: bash
