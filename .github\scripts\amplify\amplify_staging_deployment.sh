#!/bin/bash

# Ensuring jq is installed in our github actions' server
jq --version || sudo apt-get update -y && sudo apt-get install -y jq

# Referencing our temporary sts role from our server
AWS_ACCESS_KEY_ID=$(jq -r '.Credentials.AccessKeyId' /tmp/sts/sts_credentials.json)
AWS_SECRET_ACCESS_KEY=$(jq -r '.Credentials.SecretAccessKey' /tmp/sts/sts_credentials.json)
AWS_SESSION_TOKEN=$(jq -r '.Credentials.SessionToken' /tmp/sts/sts_credentials.json)
export AWS_ACCESS_KEY_ID
export AWS_SECRET_ACCESS_KEY
export AWS_SESSION_TOKEN

# Getting ID of the application from Amplify
APP_ID=$(
  aws amplify list-apps \
    --query "apps[?name=='$AWS_AMPLIFY_STAGING_APP_NAME'].appId" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --output text
)

# Getting number of applications in AWS Amplify ( as max is 5 )
APPS_TAKEN=$(
  aws amplify list-apps \
    --query "length(apps[*])" \
    --region "$AWS_FRONTEND_SLAVE_REGION"
)

# Getting number of applications' branches in AWS Amplify ( as max is 25 )
BRANCHES_TAKEN=$(
  aws amplify list-branches \
    --app-id "$APP_ID" \
    --query "length(branches[*])" \
    --region "$AWS_FRONTEND_SLAVE_REGION"
)

echo "Checking for application availability..."

# CONDITIONS:
# - Application exists in the first place
# - Number of Applications is not 25
# - Number of Application's branches doesn't exceed 50
if [ -z "$APP_ID" ]; then
  echo "Application specified does not exist !"
  exit 1
elif [ "$BRANCHES_TAKEN" -ge 50 ] && [ "$APPS_TAKEN" -eq 25 ]; then
  echo "Application branches full and number of apps reached it's maximum !"
  exit 1
elif [ "$BRANCHES_TAKEN" -ge 50 ] && [ "$APPS_TAKEN" -lt 25 ]; then
  echo "Application branches full, creating a new Application!"
  NAME_AVAILABLE="false"
  NEW_NAME="$AWS_AMPLIFY_STAGING_APP_NAME"
  COUNTER=1
  while [ "$NAME_AVAILABLE" = "false" ]; do
    NEW_NAME="${AWS_AMPLIFY_STAGING_APP_NAME}-${COUNTER}"
    APP_NAME_AVAILABLE=$(
      aws amplify list-apps \
        --query "apps[?name=='$NEW_NAME']" \
        --region "$AWS_FRONTEND_SLAVE_REGION" \
        --output text
    )
    if [ -z "$APP_NAME_AVAILABLE" ]; then
      aws amplify create-app \
        --name "$NEW_NAME" \
        --region "$AWS_FRONTEND_SLAVE_REGION"
      AWS_AMPLIFY_STAGING_APP_NAME="$NEW_NAME"
      APP_ID=$(
        aws amplify list-apps \
          --query "apps[?name=='$NEW_NAME'].appId" \
          --region "$AWS_FRONTEND_SLAVE_REGION" \
          --output text
      )
      NAME_AVAILABLE="true"
    fi
    COUNTER=$((COUNTER + 1))
  done
fi

# Conditions met, application deployment starts...
echo "App available! Starting deployment of $AWS_AMPLIFY_STAGING_APP_NAME"

# Getting Branch of the application from Amplify
BRANCH_EXISTS=$(
  aws amplify get-branch \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_STAGING_RESOURCE_NAME" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --output text 2>/dev/null
)

# CONDITIONS:
# - Branch doesn't exists, creation of branch required
# - Branch exists, no update needed (as the start deployment will work automatically)
if [ -z "$BRANCH_EXISTS" ]; then
  echo "Branch Not found! Creating..."
  aws amplify create-branch \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_STAGING_RESOURCE_NAME" \
    --region "$AWS_FRONTEND_SLAVE_REGION"
else
  echo "Branch found! Updating..."
fi

# Variable for creating name of the temporary S3 subfolder
PROJECT_NAME=$(echo "$GITHUB_REPOSITORY" | cut -d '/' -f 2)

# Deployment of new project on the branch, or updating branch with new deployment
# We are also retrieving from this new deployment the new job ID
JOB_ID=$(
  aws amplify start-deployment \
    --app-id "$APP_ID" \
    --branch-name "$AWS_AMPLIFY_STAGING_RESOURCE_NAME" \
    --source-url "s3://$AWS_S3_BUILD_BUCKET/Green-Deployments/$PROJECT_NAME/build.zip" \
    --region "$AWS_FRONTEND_SLAVE_REGION" \
    --query "jobSummary.jobId" \
    --output text
)

while true; do
  STATUS=$(
    aws amplify get-job \
      --app-id "$APP_ID" \
      --branch-name "$AWS_AMPLIFY_STAGING_RESOURCE_NAME" \
      --job-id "$JOB_ID" \
      --region "$AWS_FRONTEND_SLAVE_REGION" \
      --query "job.summary.status" \
      --output text
  )
  if [ "$STATUS" == "SUCCEED" ]; then
    SUBDOMAIN_LINK=$(
      echo "$AWS_AMPLIFY_STAGING_RESOURCE_NAME" |
        tr '[:upper:]' '[:lower:]' |
        sed 's/[^a-z0-9-]/-/g'
    )
    echo "Deployment successful, staging website's deployment link is the following:"
    echo "https://${SUBDOMAIN_LINK}.${APP_ID}.amplifyapp.com"
    break
  elif [ "$STATUS" == "FAILED" ]; then
    echo "Deployment failed."
    exit 1
  else
    echo "Deployment is still in progress..."
    sleep 5
  fi
done
