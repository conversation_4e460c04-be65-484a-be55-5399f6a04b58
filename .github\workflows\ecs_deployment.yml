name: Deploy Image to <PERSON><PERSON> ECS

on:
  workflow_call:
    inputs:
      # Name of the Image that is saved in DockerHub
      DOCKER_IMAGE:
        type: string
        required: true
      # Input needed to retrieve permissions from <PERSON>WS
      AWS_BACKEND_SSM_ROLE_PATH:
        type: string
        required: true
      # Keys Needed to Configure ECS stuff
      TASK_DEFINITION_PATH:
        type: string
        required: true
      AWS_CLUSTER_NAME:
        type: string
        required: true
      AWS_PROJECT_NAME:
        type: string
        required: true
      CONTAINER_PORT:
        type: string
        required: true
      AWS_TASKS_TO_RUN_NUMBERS:
        type: string
        required: true
      # Keys Needed to Configure Target Group stuff
      AWS_VPC_ID:
        type: string
        required: true
      # Keys Needed to Configure Security Group Stuff
      AWS_TARGET_GROUP_PATH:
        type: string
        required: true
      AWS_LOAD_BALANCER_RULE_ARN:
        type: string
        required: true
      AWS_LOAD_BALANCER_LISTENER_ARN:
        type: string
        required: true

    secrets:
      # Keys Needed to login to AWS
      AWS_BACKEND_SLAVE_ACCESS_KEY_ID:
        required: true
      AWS_BACKEND_SLAVE_SECRET_ACCESS_KEY:
        required: true
      AWS_BACKEND_SLAVE_REGION:
        required: true
      GH_TOKEN:
        required: true

jobs:
  AWS_User_Login:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          ref: "main"

      - name: Fetching shell scripts from reusable workflow
        run: |
          set -e
          mkdir ./scripts
          curl -s https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/sts/backend_sts_role_generator.sh \
          > ./scripts/backend_sts_role_generator.sh
          curl -s https://${{ secrets.GH_TOKEN }}@raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/ecs/ecs_deployment.sh \
          > ./scripts/ecs_deployment.sh

      - name: Making the scripts executable
        run: |
          set -e
          chmod +x ./scripts/backend_sts_role_generator.sh
          chmod +x ./scripts/ecs_deployment.sh

      - name: Login to AWS
        id: login-aws
        # Here, I am using an imported actions from github so I can access aws
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_BACKEND_SLAVE_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_BACKEND_SLAVE_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_BACKEND_SLAVE_REGION }}

      - name: STS Role assumption job
        # IAM user by default does not have any permissions, we associate a role to the IAM
        # for better security
        run: |
          set -e
          ./scripts/backend_sts_role_generator.sh
        shell: bash
        env:
          AWS_BACKEND_SSM_ROLE_PATH: ${{ inputs.AWS_BACKEND_SSM_ROLE_PATH }}
          AWS_BACKEND_SLAVE_REGION: ${{ secrets.AWS_BACKEND_SLAVE_REGION }}

      - name: Deployment of project on AWS
        id: deploy-aws
        run: |
          set -e
          ./scripts/ecs_deployment.sh
        shell: bash
        env:
          AWS_BACKEND_SLAVE_ACCESS_KEY_ID: ${{ secrets.AWS_BACKEND_SLAVE_ACCESS_KEY_ID }}
          AWS_BACKEND_SLAVE_SECRET_ACCESS_KEY: ${{ secrets.AWS_BACKEND_SLAVE_SECRET_ACCESS_KEY }}
          AWS_BACKEND_SLAVE_REGION: ${{ secrets.AWS_BACKEND_SLAVE_REGION }}
          TASK_DEFINITION_PATH: ${{ inputs.TASK_DEFINITION_PATH }}
          AWS_CLUSTER_NAME: ${{ inputs.AWS_CLUSTER_NAME }}
          AWS_TARGET_GROUP_PATH: ${{ inputs.AWS_TARGET_GROUP_PATH }}
          AWS_LOAD_BALANCER_LISTENER_ARN: ${{ inputs.AWS_LOAD_BALANCER_LISTENER_ARN }}
          AWS_LOAD_BALANCER_RULE_ARN: ${{ inputs.AWS_LOAD_BALANCER_RULE_ARN }}
          AWS_PROJECT_NAME: ${{ inputs.AWS_PROJECT_NAME }}
          AWS_TASKS_TO_RUN_NUMBERS: ${{ inputs.AWS_TASKS_TO_RUN_NUMBERS }}
          AWS_VPC_ID: ${{ inputs.AWS_VPC_ID }}
          CONTAINER_PORT: ${{ inputs.CONTAINER_PORT }}
          DOCKER_IMAGE: ${{ inputs.DOCKER_IMAGE }}
