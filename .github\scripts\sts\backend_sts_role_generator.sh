#!/bin/bash

set -e  # Exit on any command failure for safety

# Temporary folder to store STS role credentials
mkdir -p /tmp/sts

# Debugging: Check environment variables
echo "AWS_BACKEND_SSM_ROLE_PATH: $AWS_BACKEND_SSM_ROLE_PATH"
echo "AWS_BACKEND_SLAVE_REGION: $AWS_BACKEND_SLAVE_REGION"

# Retrieving role parameter for Slave User
SlaveBackendRole=$(
  aws ssm get-parameter \
    --name "$AWS_BACKEND_SSM_ROLE_PATH" \
    --query "Parameter.Value" \
    --region "$AWS_BACKEND_SLAVE_REGION" \
    --output text
)

# Check if the command succeeded
if [ $? -ne 0 ] || [ -z "$SlaveBackendRole" ]; then
  echo "Error: Failed to retrieve SlaveBackendRole or empty value returned."
  exit 1
fi

echo "Retrieved SlaveBackendRole: $SlaveBackendRole"

# Attaching role of Slave User in a temporary JSON file
aws sts assume-role \
  --role-arn "$SlaveBackendRole" \
  --role-session-name "SlaveBackendSession" \
  --duration-seconds 900 |
  tee /tmp/sts/sts_credentials.json

# Check if the assume-role command succeeded
if [ $? -ne 0 ]; then
  echo "Error: Failed to assume role with ARN: $SlaveBackendRole."
  exit 1
fi

echo "Role assumption is complete!"
