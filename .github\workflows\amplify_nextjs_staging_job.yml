name: Next.js Staging job for Amplify deployment

on:
  workflow_call:
    inputs:
      AWS_AMPLIFY_STAGING_APP_NAME:
        type: string
        required: true
      AWS_AMPLIFY_STAGING_RESOURCE_NAME:
        type: string
        required: true
      AWS_FRONTEND_SSM_ROLE_PATH:
        type: string
        required: true
      GITHUB_REPOSITORY_URL:
        required: true
        type: string
      NEXTJS_ENV_VARS:
        required: false
        type: string
    secrets:
      AWS_FRONTEND_SLAVE_ACCESS_KEY_ID:
        required: true
      AWS_FRONTEND_SLAVE_SECRET_ACCESS_KEY:
        required: true
      AWS_FRONTEND_SLAVE_REGION:
        required: true
      GH_TOKEN:
        required: true

jobs:
  AWS_NextJS_Staging_Deployment:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Fetch shell scripts from repository
        run: |
          set -e
          mkdir -p ./scripts/sts
          mkdir -p ./scripts/amplify

          # Try multiple methods to get the scripts
          echo "Attempting to fetch scripts..."

          # Method 1: Try with GitHub API
          echo "Method 1: Using GitHub API..."
          if curl -f -H "Authorization: token ${{ secrets.GH_TOKEN }}" \
             -H "Accept: application/vnd.github.v3.raw" \
             -s "https://api.github.com/repos/Modular3D/UniversalCICDPipelines/contents/.github/scripts/sts/frontend_sts_role_generator.sh" \
             > ./scripts/sts/frontend_sts_role_generator.sh 2>/dev/null && \
             curl -f -H "Authorization: token ${{ secrets.GH_TOKEN }}" \
             -H "Accept: application/vnd.github.v3.raw" \
             -s "https://api.github.com/repos/Modular3D/UniversalCICDPipelines/contents/.github/scripts/amplify/amplify_nextjs_staging_deployment.sh" \
             > ./scripts/amplify/amplify_nextjs_staging_deployment.sh 2>/dev/null; then
            echo "GitHub API method successful"
          else
            echo "GitHub API method failed, trying raw.githubusercontent.com..."

            # Method 2: Try with raw.githubusercontent.com
            if curl -f -H "Authorization: token ${{ secrets.GH_TOKEN }}" \
               -s "https://raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/sts/frontend_sts_role_generator.sh" \
               > ./scripts/sts/frontend_sts_role_generator.sh 2>/dev/null && \
               curl -f -H "Authorization: token ${{ secrets.GH_TOKEN }}" \
               -s "https://raw.githubusercontent.com/Modular3D/UniversalCICDPipelines/main/.github/scripts/amplify/amplify_nextjs_staging_deployment.sh" \
               > ./scripts/amplify/amplify_nextjs_staging_deployment.sh 2>/dev/null; then
              echo "Raw GitHub method successful"
            else
              echo "All methods failed. Checking what we received..."
              echo "STS script content:"
              head -5 ./scripts/sts/frontend_sts_role_generator.sh || echo "No STS script content"
              echo "Amplify script content:"
              head -5 ./scripts/amplify/amplify_nextjs_staging_deployment.sh || echo "No Amplify script content"
              exit 1
            fi
          fi

          # Verify scripts are valid
          echo "Verifying downloaded scripts..."
          if [ ! -s ./scripts/sts/frontend_sts_role_generator.sh ]; then
            echo "STS script is empty or doesn't exist"
            exit 1
          fi

          if [ ! -s ./scripts/amplify/amplify_nextjs_staging_deployment.sh ]; then
            echo "Amplify script is empty or doesn't exist"
            exit 1
          fi

          # Check if scripts start with shebang (not HTML error page)
          if ! head -1 ./scripts/sts/frontend_sts_role_generator.sh | grep -q "^#!/bin/bash"; then
            echo "STS script doesn't appear to be a valid bash script. Content:"
            head -5 ./scripts/sts/frontend_sts_role_generator.sh
            exit 1
          fi

          if ! head -1 ./scripts/amplify/amplify_nextjs_staging_deployment.sh | grep -q "^#!/bin/bash"; then
            echo "Amplify script doesn't appear to be a valid bash script. Content:"
            head -5 ./scripts/amplify/amplify_nextjs_staging_deployment.sh
            exit 1
          fi

          echo "Scripts downloaded and verified successfully"

      - name: Making the scripts executable
        run: |
          set -e
          chmod +x ./scripts/sts/frontend_sts_role_generator.sh
          chmod +x ./scripts/amplify/amplify_nextjs_staging_deployment.sh

      - name: Login to AWS
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_FRONTEND_SLAVE_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_FRONTEND_SLAVE_SECRET_ACCESS_KEY }}
          aws-region: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}

      - name: STS Role assumption job
        run: |
          set -e
          ./scripts/sts/frontend_sts_role_generator.sh
        shell: bash
        env:
          AWS_FRONTEND_SSM_ROLE_PATH: ${{ inputs.AWS_FRONTEND_SSM_ROLE_PATH }}
          AWS_FRONTEND_SLAVE_REGION: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}

      - name: Deployment of Next.js project on AWS Amplify
        run: |
          set -e
          ./scripts/amplify/amplify_nextjs_staging_deployment.sh
        shell: bash
        env:
          AWS_FRONTEND_SLAVE_REGION: ${{ secrets.AWS_FRONTEND_SLAVE_REGION }}
          AWS_AMPLIFY_STAGING_APP_NAME: ${{ inputs.AWS_AMPLIFY_STAGING_APP_NAME }}
          AWS_AMPLIFY_STAGING_RESOURCE_NAME: ${{ inputs.AWS_AMPLIFY_STAGING_RESOURCE_NAME }}
          GITHUB_REPOSITORY_URL: ${{ inputs.GITHUB_REPOSITORY_URL }}
          GITHUB_ACCESS_TOKEN: ${{ secrets.GH_TOKEN }}
          NEXTJS_ENV_VARS: ${{ inputs.NEXTJS_ENV_VARS }}
